@use 'variables.scss';
@use 'z-index.scss';
@use 'animations.scss';
@use 'components/terminal.scss';
@use 'components/resize-handle.scss';
@use 'components/code.scss';
@use 'components/editor.scss';
@use 'components/toast.scss';

html,
body {
  height: 100%;
  width: 100%;
}

:root {
  --gradient-opacity: 0.8;
  --primary-color: rgba(59, 130, 246, var(--gradient-opacity)); // #3B82F6 - Blue
  --secondary-color: rgba(147, 51, 234, var(--gradient-opacity)); // #9333EA - Purple
  --accent-color: rgba(99, 102, 241, var(--gradient-opacity)); // Mix of blue and purple
}

.modern-scrollbar {
  overflow: auto;

  // WebKit scrollbar styling
  &::-webkit-scrollbar {
    width: 2px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    // Use CSS variables for colors
    background-color: var(--modern-scrollbar-thumb-background);
    border-radius: 9999px; // pill shape
    border: 2px solid transparent; // for padding-like effect
    background-clip: content-box;
    transition: background-color 0.2s ease-in-out; // Add transition
  }

  &::-webkit-scrollbar-thumb:hover {
    // Use CSS variable for hover color
    background-color: var(--modern-scrollbar-thumb-backgroundHover);
  }

  // Firefox support
  scrollbar-width: thin;
  // Use CSS variables for Firefox colors
  scrollbar-color: var(--modern-scrollbar-thumb-backgroundHover) transparent; // Use hover color for thumb for consistency
}

.modern-scrollbar-invert {
  &::-webkit-scrollbar-thumb {
    // Override with a contrasting color, e.g., primary text color with transparency
    background-color: color-mix(in srgb, var(--bolt-elements-textPrimary), transparent 70%);
  }

  &::-webkit-scrollbar-thumb:hover {
    // Darker/more opaque version on hover
    background-color: color-mix(in srgb, var(--bolt-elements-textPrimary), transparent 50%);
  }

  // Firefox support for inverted colors
  scrollbar-color: color-mix(in srgb, var(--bolt-elements-textPrimary), transparent 50%) transparent;
}

// Global button fixes to prevent white backgrounds
button {
  // Remove default browser button styling for buttons without explicit backgrounds
  &:not([class*="bg-"]):not([style*="background"]):not(.genvibe-button-primary):not(.genvibe-button-secondary) {
    border: none;
    outline: none;
    background: transparent !important;
  }

  // Fix for buttons with Tailwind background classes
  &[class*="bg-gradient-to-"] {
    border: none !important;
    outline: none !important;
  }

  // Fix for buttons with specific background colors
  &[class*="bg-blue-"],
  &[class*="bg-green-"],
  &[class*="bg-red-"],
  &[class*="bg-purple-"],
  &[class*="bg-gray-"],
  &[class*="bg-white"] {
    border: none !important;
    outline: none !important;
  }
}

// Specific fixes for GenVibe buttons
.genvibe-button-primary {
  background: linear-gradient(to right, #3B82F6, #9333EA) !important;
  border: none !important;
  outline: none !important;
  color: white !important;
}

.genvibe-button-secondary {
  background: transparent !important;
  border: 1px solid var(--bolt-elements-borderColor) !important;
  outline: none !important;
}

// Fix for example prompt buttons and other GenVibe components
.example-prompt-button,
button[class*="from-blue-500"],
button[class*="to-purple-600"] {
  background: transparent !important;

  &:hover {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1)) !important;
  }
}

// Ensure no white backgrounds on any interactive elements
input:not([class*="bg-"]),
textarea:not([class*="bg-"]),
select:not([class*="bg-"]) {
  background: transparent !important;
}
