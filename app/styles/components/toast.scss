.Toastify__toast {
  --at-apply: shadow-md;

  background-color: var(--bolt-elements-bg-depth-2);
  color: var(--bolt-elements-textPrimary);
  border: 1px solid var(--bolt-elements-borderColor);
}

.Toastify__close-button {
  color: var(--bolt-elements-item-contentDefault);
  opacity: 1;
  transition: none;

  &:hover {
    color: var(--bolt-elements-item-contentActive);
  }
}

/* Modern Toast Styles */
.modern-toast {
  border-radius: 12px !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  font-weight: 500;
  font-size: 14px;
  padding: 16px 20px !important;
  min-height: auto !important;

  &.modern-toast-warning {
    background: linear-gradient(135deg,
      rgba(251, 191, 36, 0.15) 0%,
      rgba(245, 158, 11, 0.1) 100%) !important;
    border-color: rgba(251, 191, 36, 0.3) !important;
    color: #f59e0b !important;

    .Toastify__close-button {
      color: #f59e0b !important;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }

  &.modern-toast-error {
    background: linear-gradient(135deg,
      rgba(239, 68, 68, 0.15) 0%,
      rgba(220, 38, 38, 0.1) 100%) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #ef4444 !important;

    .Toastify__close-button {
      color: #ef4444 !important;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }

  &.modern-toast-success {
    background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.15) 0%,
      rgba(22, 163, 74, 0.1) 100%) !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
    color: #22c55e !important;

    .Toastify__close-button {
      color: #22c55e !important;
      opacity: 0.8;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.modern-toast-body {
  padding: 0 !important;
  margin: 0 !important;
  font-weight: 500;
  line-height: 1.4;
}
