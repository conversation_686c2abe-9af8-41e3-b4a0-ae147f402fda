import { toast } from 'react-toastify';
import Cookies from 'js-cookie';
import { CODING_MODELS, getRecommendedModel } from '~/lib/modules/llm/types';

/**
 * Universal Error Handler for LLM API calls
 * Provides smart error messages and automatic fallback to free models
 */

export interface ErrorHandlerOptions {
  currentModel: string;
  currentProvider: string;
  onModelSwitch?: (newModel: string) => void;
  onProviderSwitch?: (newProvider: string) => void;
}

export interface ParsedError {
  type: 'quota' | 'billing' | 'auth' | 'rate_limit' | 'network' | 'unknown';
  message: string;
  suggestedAction: string;
  fallbackModel?: string;
  fallbackProvider?: string;
  shouldAutoFallback: boolean;
}

/**
 * Parse error from API response and determine appropriate action
 */
export function parseApiError(error: any, currentModel: string, currentProvider: string): ParsedError {
  // Handle AI SDK (Vercel AI) error format
  let errorMessage = '';
  let errorCode = '';
  let responseBody = '';

  if (error?.message) {
    errorMessage = error.message;
  }

  if (error?.statusCode) {
    errorCode = error.statusCode.toString();
  }

  if (error?.responseBody) {
    responseBody = error.responseBody;
    try {
      const parsedBody = JSON.parse(responseBody);
      if (parsedBody?.error?.message) {
        errorMessage = parsedBody.error.message;
      }
      if (parsedBody?.error?.code) {
        errorCode = parsedBody.error.code.toString();
      }
    } catch (e) {
      // Ignore JSON parse errors
    }
  }

  // Fallback to nested error properties
  if (!errorMessage) {
    errorMessage = error?.error?.message || error?.toString() || 'Unknown error';
  }
  if (!errorCode) {
    errorCode = error?.code || error?.error?.code || '';
  }

  console.log('🔍 Parsing API error:', {
    errorMessage,
    errorCode,
    currentModel,
    currentProvider,
    hasResponseBody: !!responseBody,
    statusCode: error?.statusCode
  });

  // Google/Gemini specific errors
  if (currentProvider === 'Google') {
    // Pro model without free tier (specific error we're seeing)
    if (errorMessage.includes("doesn't have a free quota tier") ||
        errorMessage.includes("free quota tier") ||
        (errorCode === '429' && errorMessage.includes('Pro'))) {
      return {
        type: 'billing',
        message: `${currentModel} requires billing (no free tier)`,
        suggestedAction: 'Switching to free model',
        fallbackModel: 'gemini-2.0-flash',
        shouldAutoFallback: true
      };
    }

    // Quota exceeded errors
    if (errorMessage.includes('quota') || errorMessage.includes('QUOTA_EXCEEDED') ||
        errorMessage.includes('Resource has been exhausted') ||
        (errorCode === '429' && errorMessage.includes('quota'))) {
      return {
        type: 'quota',
        message: `${currentModel} quota exceeded`,
        suggestedAction: 'Switching to free model with higher limits',
        fallbackModel: 'gemini-2.0-flash',
        shouldAutoFallback: true
      };
    }

    // Billing/payment required errors
    if (errorMessage.includes('billing') || errorMessage.includes('BILLING_NOT_ACTIVE') ||
        errorMessage.includes('payment') || errorMessage.includes('requires billing')) {
      return {
        type: 'billing',
        message: `${currentModel} requires billing to be enabled`,
        suggestedAction: 'Switching to free model',
        fallbackModel: 'gemini-2.0-flash',
        shouldAutoFallback: true
      };
    }

    // API key errors
    if (errorMessage.includes('API key') || errorMessage.includes('UNAUTHENTICATED') ||
        errorMessage.includes('authentication') || errorCode === '401') {
      return {
        type: 'auth',
        message: 'Invalid or missing Google API key',
        suggestedAction: 'Please check your API key in settings',
        shouldAutoFallback: false
      };
    }

    // Rate limiting
    if (errorMessage.includes('rate limit') || errorMessage.includes('RATE_LIMIT_EXCEEDED') ||
        errorMessage.includes('Too many requests')) {
      return {
        type: 'rate_limit',
        message: 'Rate limit exceeded',
        suggestedAction: 'Please wait a moment and try again',
        shouldAutoFallback: false
      };
    }
  }

  // OpenAI specific errors
  if (currentProvider === 'OpenAI') {
    // Quota/billing errors
    if (errorMessage.includes('quota') || errorMessage.includes('insufficient_quota') ||
        errorMessage.includes('billing') || errorMessage.includes('payment_required')) {
      return {
        type: 'billing',
        message: `${currentModel} requires active billing`,
        suggestedAction: 'All OpenAI models require billing. Try Google Gemini (free)',
        fallbackModel: 'gemini-2.0-flash',
        fallbackProvider: 'Google',
        shouldAutoFallback: true
      };
    }

    // API key errors
    if (errorMessage.includes('API key') || errorMessage.includes('Unauthorized') ||
        errorCode === '401') {
      return {
        type: 'auth',
        message: 'Invalid or missing OpenAI API key',
        suggestedAction: 'Please check your API key in settings',
        shouldAutoFallback: false
      };
    }

    // Rate limiting
    if (errorMessage.includes('rate_limit') || errorMessage.includes('Too Many Requests')) {
      return {
        type: 'rate_limit',
        message: 'OpenAI rate limit exceeded',
        suggestedAction: 'Please wait a moment and try again',
        shouldAutoFallback: false
      };
    }
  }

  // Anthropic specific errors
  if (currentProvider === 'Anthropic') {
    // Billing errors
    if (errorMessage.includes('credit') || errorMessage.includes('billing') ||
        errorMessage.includes('payment') || errorMessage.includes('insufficient')) {
      return {
        type: 'billing',
        message: `${currentModel} requires active billing`,
        suggestedAction: 'All Claude models require billing. Try Google Gemini (free)',
        fallbackModel: 'gemini-2.0-flash',
        fallbackProvider: 'Google',
        shouldAutoFallback: true
      };
    }

    // API key errors
    if (errorMessage.includes('API key') || errorMessage.includes('authentication') ||
        errorCode === '401') {
      return {
        type: 'auth',
        message: 'Invalid or missing Anthropic API key',
        suggestedAction: 'Please check your API key in settings',
        shouldAutoFallback: false
      };
    }
  }

  // Network errors
  if (errorMessage.includes('network') || errorMessage.includes('fetch') ||
      errorMessage.includes('connection') || errorMessage.includes('timeout')) {
    return {
      type: 'network',
      message: 'Network connection error',
      suggestedAction: 'Please check your internet connection and try again',
      shouldAutoFallback: false
    };
  }

  // Generic fallback
  return {
    type: 'unknown',
    message: errorMessage.length > 100 ? errorMessage.substring(0, 100) + '...' : errorMessage,
    suggestedAction: 'Please try again or switch to a different model',
    shouldAutoFallback: false
  };
}

/**
 * Handle API error with smart fallback and user-friendly messages
 */
export function handleApiError(error: any, options: ErrorHandlerOptions): boolean {
  const { currentModel, currentProvider, onModelSwitch, onProviderSwitch } = options;

  console.log('🔧 Universal error handler called with:', {
    currentModel,
    currentProvider,
    errorType: typeof error,
    errorMessage: error?.message,
    statusCode: error?.statusCode,
    hasResponseBody: !!error?.responseBody
  });

  const parsedError = parseApiError(error, currentModel, currentProvider);

  console.log('🔧 Parsed error result:', parsedError);

  if (parsedError.shouldAutoFallback && parsedError.fallbackModel) {
    // Auto-fallback to suggested model
    const fallbackModel = parsedError.fallbackModel;
    const fallbackProvider = parsedError.fallbackProvider || currentProvider;

    toast.error(
      `${parsedError.message}. ${parsedError.suggestedAction}...`,
      {
        position: 'bottom-right',
        autoClose: 4000,
        className: 'modern-toast modern-toast-error',
        bodyClassName: 'modern-toast-body',
      }
    );

    // Auto-switch after a short delay
    setTimeout(() => {
      if (fallbackProvider !== currentProvider && onProviderSwitch) {
        onProviderSwitch(fallbackProvider);
        Cookies.set('selectedProvider', fallbackProvider);
      }

      if (onModelSwitch) {
        onModelSwitch(fallbackModel);
        Cookies.set('selectedModel', fallbackModel);
      }

      toast.success(
        `Switched to ${fallbackModel}. Please try your request again.`,
        {
          position: 'bottom-right',
          autoClose: 4000,
          className: 'modern-toast modern-toast-success',
          bodyClassName: 'modern-toast-body',
        }
      );
    }, 1500);

    return true; // Error was handled
  } else {
    // Show error message without auto-fallback
    const message = parsedError.type === 'auth'
      ? `${parsedError.message}. ${parsedError.suggestedAction}`
      : `${parsedError.message}. ${parsedError.suggestedAction}`;

    toast.error(message, {
      position: 'bottom-right',
      autoClose: parsedError.type === 'auth' ? 8000 : 5000,
      className: 'modern-toast modern-toast-error',
      bodyClassName: 'modern-toast-body',
    });

    return false; // Error was not auto-handled
  }
}

/**
 * Get best free model for a provider
 */
export function getBestFreeModel(provider: string): string | null {
  const freeModel = getRecommendedModel(provider, false);
  if (freeModel) return freeModel;

  // Fallback to known free models
  switch (provider) {
    case 'Google':
      return 'gemini-2.0-flash';
    case 'OpenAI':
      return null; // No free OpenAI models
    case 'Anthropic':
      return null; // No free Anthropic models
    default:
      return null;
  }
}

/**
 * Get best free provider and model combination
 */
export function getBestFreeOption(): { provider: string; model: string } {
  // Google has the best free models
  return {
    provider: 'Google',
    model: 'gemini-2.0-flash'
  };
}
