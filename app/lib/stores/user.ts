import { atom, computed } from 'nanostores';
import type { User, Session } from '@supabase/supabase-js';
import { supabase } from '~/lib/supabase/client';
import { toast } from 'react-toastify';
import { ApiKeyService } from '~/lib/services/apiKeyService';

// Security imports
let checkRateLimit: any;
let logSecurityEvent: any;

// Lazy load security modules to avoid circular dependencies
const loadSecurityModules = async () => {
  if (!checkRateLimit || !logSecurityEvent) {
    try {
      const [requestValidation, apiKeyEncryption] = await Promise.all([
        import('~/lib/security/requestValidation'),
        import('~/lib/security/apiKeyEncryption'),
      ]);
      checkRateLimit = requestValidation.checkRateLimit;
      logSecurityEvent = apiKeyEncryption.logSecurityEvent;
    } catch (error) {
      console.warn('Security modules not available:', error);
      // Fallback implementations
      checkRateLimit = () => ({ allowed: true });
      logSecurityEvent = () => {};
    }
  }
};

// Types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  username?: string;
  subscription_tier: 'free' | 'pro';
  subscription_status: 'active' | 'cancelled' | 'expired';
  daily_message_count: number;
  daily_message_limit: number;
  total_messages_sent: number;
  total_projects_created: number;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  remainingMessages: number;
}

// Stores
export const authState = atom<AuthState>({
  user: null,
  session: null,
  profile: null,
  isLoading: false, // Start with false to prevent infinite loading
  isAuthenticated: false,
  remainingMessages: 0,
});

// Computed values
export const isAuthenticated = computed(authState, (state) => state.isAuthenticated);
export const currentUser = computed(authState, (state) => state.user);
export const userProfile = computed(authState, (state) => state.profile);
export const isProUser = computed(authState, (state) => state.profile?.subscription_tier === 'pro');
export const remainingMessages = computed(authState, (state) => state.remainingMessages);

// Internal flag to coordinate between initialize() and auth state listener
let isInitializing = false;

// Actions
export const userActions = {
  // Initialize auth state - simplified approach with timeout
  async initialize() {
    if (isInitializing) {
      console.log('🚀 INITIALIZE: Already initializing, skipping...');
      return;
    }

    try {
      isInitializing = true;
      console.log('🚀 INITIALIZE: Starting auth initialization...');

      // Load security modules
      await loadSecurityModules();

      // Set loading state initially
      authState.set({ ...authState.get(), isLoading: true });

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Auth initialization timeout')), 5000);
      });

      // Check for existing session with timeout
      const sessionPromise = supabase.auth.getSession();

      const { data: { session }, error } = await Promise.race([sessionPromise, timeoutPromise]) as any;

      if (error) {
        console.error('🚀 INITIALIZE: Error getting session:', error);
        authState.set({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isAuthenticated: false,
          remainingMessages: 0,
        });
        return;
      }

      if (session?.user) {
        console.log('🚀 INITIALIZE: Found existing session, setting user');
        await this.setUser(session.user, session, false);
      } else {
        console.log('🚀 INITIALIZE: No existing session found');
        authState.set({
          user: null,
          session: null,
          profile: null,
          isLoading: false,
          isAuthenticated: false,
          remainingMessages: 0,
        });
      }

    } catch (error) {
      console.error('🚀 INITIALIZE: Auth initialization error:', error);
      authState.set({
        user: null,
        session: null,
        profile: null,
        isLoading: false,
        isAuthenticated: false,
        remainingMessages: 0,
      });
    } finally {
      isInitializing = false;
      console.log('🚀 INITIALIZE: Initialization complete');
    }
  },

  // Set user and fetch profile - simplified for faster loading
  async setUser(user: User, session: Session, showSuccessToast = false) {
    try {
      console.log('🔧 SET_USER: Starting setUser for:', user.email);

      // Create profile from auth data immediately (no database blocking)
      console.log('🔧 SET_USER: Creating profile from auth user data...');

      // Handle avatar URL properly (especially for Google profile images)
      let avatarUrl = user.user_metadata?.avatar_url || user.user_metadata?.picture || null;
      if (avatarUrl) {
        // For Google profile images, ensure we get a reasonable size and remove problematic parameters
        if (avatarUrl.includes('googleusercontent.com')) {
          // Remove size parameters and add a standard size
          avatarUrl = avatarUrl.split('=')[0] + '=s200-c';
        }
        console.log('🔧 SET_USER: Processed avatar URL:', avatarUrl);
      }

      // Create profile from auth user data (fast, no database blocking)
      const profile: UserProfile = {
        id: user.id,
        email: user.email || '',
        full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
        avatar_url: avatarUrl,
        username: user.user_metadata?.username || null,
        subscription_tier: 'free', // Default to free tier
        subscription_status: 'active',
        daily_message_count: 0,
        daily_message_limit: 10,
        total_messages_sent: 0,
        total_projects_created: 0,
        preferences: {},
        created_at: user.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('🔧 SET_USER: Profile created from auth data:', profile);

      // Set auth state immediately (don't wait for database)
      const newAuthState = {
        user,
        session,
        profile,
        isLoading: false,
        isAuthenticated: true,
        remainingMessages: 10,
      };

      console.log('🔧 SET_USER: Setting auth state immediately:', newAuthState);
      authState.set(newAuthState);

      // Sync with database in background (non-blocking)
      this.syncProfileToDatabase(user, profile).catch(error => {
        console.error('🔧 SET_USER: Background database sync failed:', error);
      });

      // Sync API keys from Supabase to cookies for compatibility
      this.syncApiKeysToCookies(user.id).catch(error => {
        console.error('🔧 SET_USER: Background API key sync failed:', error);
      });

      // Note: Success toast is handled by the landing page when login=success parameter is present
      // This prevents duplicate toasts on every page reload

      console.log('🔧 SET_USER: setUser completed successfully');
    } catch (error) {
      console.error('🔧 SET_USER: Set user error:', error);

      // Fallback auth state - still set user as authenticated
      const fallbackAuthState = {
        user,
        session,
        profile: null,
        isLoading: false,
        isAuthenticated: true,
        remainingMessages: 10,
      };
      console.log('🔧 SET_USER: Setting fallback auth state:', fallbackAuthState);
      authState.set(fallbackAuthState);
    }
  },

  // Background database sync (non-blocking)
  async syncProfileToDatabase(user: User, profile: UserProfile) {
    try {
      console.log('🔄 SYNC_PROFILE: Starting background database sync...');

      // Try to fetch existing profile first
      const { data: existingProfile, error: fetchError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔄 SYNC_PROFILE: Error fetching existing profile:', fetchError);
        return;
      }

      if (existingProfile) {
        console.log('🔄 SYNC_PROFILE: Profile exists in database, updating local state...');
        // Update local state with database data and calculate remaining messages
        const currentState = authState.get();
        const remainingMessages = Math.max(0, (existingProfile.daily_message_limit || 10) - (existingProfile.daily_message_count || 0));

        authState.set({
          ...currentState,
          profile: existingProfile,
          remainingMessages: remainingMessages,
        });

        console.log('🔄 SYNC_PROFILE: Updated with database data', {
          dailyCount: existingProfile.daily_message_count,
          dailyLimit: existingProfile.daily_message_limit,
          remaining: remainingMessages
        });
      } else {
        console.log('🔄 SYNC_PROFILE: Creating new profile in database...');
        // Create new profile in database
        const { error: insertError } = await supabase
          .from('user_profiles')
          .insert([profile]);

        if (insertError) {
          console.error('🔄 SYNC_PROFILE: Error creating profile:', insertError);
        } else {
          console.log('🔄 SYNC_PROFILE: Profile created successfully in database');
        }
      }
    } catch (error) {
      console.error('🔄 SYNC_PROFILE: Background sync error:', error);
    }
  },

  // Background API key sync to cookies (non-blocking)
  async syncApiKeysToCookies(userId: string) {
    try {
      console.log('🔄 SYNC_API_KEYS: Starting background API key sync to cookies...');
      await ApiKeyService.syncApiKeysToCookies(supabase, userId);
      console.log('🔄 SYNC_API_KEYS: API keys synced to cookies successfully');
    } catch (error) {
      console.error('🔄 SYNC_API_KEYS: Background API key sync error:', error);
    }
  },

  // Clear user state
  clearUser() {
    console.log('🧹 CLEAR_USER: Clearing user state...');
    authState.set({
      user: null,
      session: null,
      profile: null,
      isLoading: false,
      isAuthenticated: false,
      remainingMessages: 0,
    });
    console.log('🧹 CLEAR_USER: User state cleared');
  },

  // Complete auth cleanup - clears all auth data including localStorage
  async completeSignOut() {
    console.log('🧹 COMPLETE_SIGNOUT: Starting complete sign out...');

    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('🧹 COMPLETE_SIGNOUT: Supabase signOut error:', error);
      }

      // Clear localStorage auth data
      if (typeof window !== 'undefined') {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('genvibe-auth') || key.includes('supabase'))) {
            keysToRemove.push(key);
          }
        }

        keysToRemove.forEach(key => {
          console.log('🧹 COMPLETE_SIGNOUT: Removing localStorage key:', key);
          localStorage.removeItem(key);
        });
      }

      // Clear user state
      this.clearUser();

      console.log('🧹 COMPLETE_SIGNOUT: Complete sign out finished');
      return { error: null };
    } catch (error) {
      console.error('🧹 COMPLETE_SIGNOUT: Error during complete sign out:', error);
      return { error };
    }
  },

  // Get remaining messages for today
  async getRemainingMessages(userId: string): Promise<number> {
    // For now, return default values without database queries
    const currentState = authState.get();

    if (currentState.profile?.subscription_tier === 'pro') {
      return -1; // Unlimited for pro users
    }

    // Return the current remaining messages from state, or default to 10
    return currentState.remainingMessages || 10;
  },

  // Check if user can send message
  async canSendMessage(userId: string): Promise<boolean> {
    try {
      const remaining = await userActions.getRemainingMessages(userId);

      // Pro users (remaining = -1) or users with messages left can send
      return remaining === -1 || remaining > 0;
    } catch (error) {
      console.error('Check message limit error:', error);
      return true; // Allow message if check fails (graceful degradation)
    }
  },

  // Update profile
  async updateProfile(updates: Partial<UserProfile>) {
    const state = authState.get();
    if (!state.user || !state.profile) return { error: 'No user or profile found' };

    try {
      const updatedProfile = {
        ...state.profile,
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Update in database first
      const { data: savedProfile, error: updateError } = await supabase
        .from('user_profiles')
        .update(updatedProfile)
        .eq('id', state.user.id)
        .select()
        .single();

      if (updateError) {
        console.error('🔧 UPDATE_PROFILE: Database update error:', updateError);
        // Still update local state as fallback
        authState.set({
          ...state,
          profile: updatedProfile,
        });
        return { data: updatedProfile, warning: 'Updated locally but failed to save to database' };
      }

      // Update local state with saved data
      authState.set({
        ...state,
        profile: savedProfile,
      });

      console.log('🔧 Profile updated in database and locally:', savedProfile);
      return { data: savedProfile };
    } catch (error) {
      console.error('Update profile error:', error);
      return { error };
    }
  },

  // Track message usage with atomic updates
  async trackMessageUsage(userId: string, tokensUsed: number = 0, modelUsed: string = '') {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      console.log('🔧 TRACK_MESSAGE: Starting atomic message tracking...', { userId, tokensUsed, modelUsed });

      // Use atomic increment to prevent race conditions
      const { data: existing, error: fetchError } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔧 TRACK_MESSAGE: Error fetching existing usage:', fetchError);
        return;
      }

      if (existing) {
        // Atomic increment of existing record
        const { error: updateError } = await supabase
          .from('usage_tracking')
          .update({
            messages_sent: (existing.messages_sent || 0) + 1,
            tokens_used: (existing.tokens_used || 0) + tokensUsed,
            api_calls_made: (existing.api_calls_made || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId)
          .eq('date', today);

        if (updateError) {
          console.error('🔧 TRACK_MESSAGE: Error updating usage:', updateError);
        } else {
          console.log('✅ TRACK_MESSAGE: Usage updated successfully');
        }
      } else {
        // Create new record
        const { error: insertError } = await supabase
          .from('usage_tracking')
          .insert({
            user_id: userId,
            date: today,
            messages_sent: 1,
            tokens_used: tokensUsed,
            api_calls_made: 1,
            projects_created: 0,
            subscription_tier: authState.get().profile?.subscription_tier || 'free',
          });

        if (insertError) {
          console.error('🔧 TRACK_MESSAGE: Error inserting usage:', insertError);
        } else {
          console.log('✅ TRACK_MESSAGE: New usage record created');
        }
      }

      // Update user profile message count atomically
      const state = authState.get();
      if (state.profile) {
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .update({
            daily_message_count: (state.profile.daily_message_count || 0) + 1,
            total_messages_sent: (state.profile.total_messages_sent || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId)
          .select()
          .single();

        if (profileError) {
          console.error('🔧 TRACK_MESSAGE: Error updating profile:', profileError);
        } else {
          // Update local state with fresh data and calculate remaining messages
          const newRemainingMessages = Math.max(0, (profileData.daily_message_limit || 10) - (profileData.daily_message_count || 0));

          authState.set({
            ...state,
            profile: profileData,
            remainingMessages: newRemainingMessages,
          });
          console.log('✅ TRACK_MESSAGE: Profile updated successfully', {
            dailyCount: profileData.daily_message_count,
            dailyLimit: profileData.daily_message_limit,
            remaining: newRemainingMessages
          });
        }
      }
    } catch (error) {
      console.error('🔧 TRACK_MESSAGE: Error:', error);
    }
  },

  // Track project creation with atomic updates
  async trackProjectCreation(userId: string, projectData: any) {
    try {
      const today = new Date().toISOString().split('T')[0];

      console.log('🔧 TRACK_PROJECT: Starting atomic project tracking...', { userId, projectData });

      // Use atomic increment to prevent race conditions
      const { data: existing, error: fetchError } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('🔧 TRACK_PROJECT: Error fetching existing usage:', fetchError);
        return;
      }

      if (existing) {
        // Atomic increment of existing record - only increment projects_created
        const { error: updateError } = await supabase
          .from('usage_tracking')
          .update({
            projects_created: (existing.projects_created || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId)
          .eq('date', today);

        if (updateError) {
          console.error('🔧 TRACK_PROJECT: Error updating usage:', updateError);
        } else {
          console.log('✅ TRACK_PROJECT: Usage updated successfully');
        }
      } else {
        // Create new record
        const { error: insertError } = await supabase
          .from('usage_tracking')
          .insert({
            user_id: userId,
            date: today,
            messages_sent: 0,
            tokens_used: 0,
            api_calls_made: 0,
            projects_created: 1,
            subscription_tier: authState.get().profile?.subscription_tier || 'free',
          });

        if (insertError) {
          console.error('🔧 TRACK_PROJECT: Error inserting usage:', insertError);
        } else {
          console.log('✅ TRACK_PROJECT: New usage record created');
        }
      }

      // Update user profile project count atomically
      const state = authState.get();
      if (state.profile) {
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .update({
            total_projects_created: (state.profile.total_projects_created || 0) + 1,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId)
          .select()
          .single();

        if (profileError) {
          console.error('🔧 TRACK_PROJECT: Error updating profile:', profileError);
        } else {
          // Update local state with fresh data
          authState.set({
            ...state,
            profile: profileData,
          });
          console.log('✅ TRACK_PROJECT: Profile updated successfully');
        }
      }

      console.log('🔧 TRACK_PROJECT: Project creation tracked');
    } catch (error) {
      console.error('🔧 TRACK_PROJECT: Error:', error);
    }
  },

  // Sign out
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
      }
      userActions.clearUser();
      return { error };
    } catch (error) {
      console.error('Sign out error:', error);
      userActions.clearUser();
      return { error };
    }
  },

  // Debug utility to check localStorage auth data
  debugAuthStorage() {
    if (typeof window === 'undefined') return;

    console.log('🔍 DEBUG: Checking localStorage for auth data...');
    const authKeys = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('genvibe-auth') || key.includes('supabase'))) {
        const value = localStorage.getItem(key);
        authKeys.push({ key, hasValue: !!value, valueLength: value?.length || 0 });
      }
    }

    console.log('🔍 DEBUG: Auth-related localStorage keys:', authKeys);
    return authKeys;
  },
};

// Initialize auth state listener - handles auth state changes after initialization
if (typeof window !== 'undefined') {
  console.log('🔧 Setting up auth state listener...');

  const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
    console.log('🔄 Auth state changed:', event, session?.user?.email);

    // Skip INITIAL_SESSION since we handle it in initialize()
    if (event === 'INITIAL_SESSION') {
      console.log('🚀 INITIAL_SESSION event - skipping (handled in initialize)');
      return;
    }

    // Track if this is a fresh sign-in vs session restoration
    const currentState = authState.get();
    const wasAuthenticated = currentState.isAuthenticated;

    if (event === 'SIGNED_IN' && session?.user) {
      // Only show toast for actual new sign-ins, not session restoration
      const isNewSignIn = !wasAuthenticated && !isInitializing;
      console.log('✅ SIGNED_IN event - isNewSignIn:', isNewSignIn);
      await userActions.setUser(session.user, session, isNewSignIn);
    } else if (event === 'SIGNED_OUT') {
      console.log('❌ SIGNED_OUT event - clearing user');
      userActions.clearUser();
    } else if (event === 'TOKEN_REFRESHED' && session?.user) {
      console.log('🔄 TOKEN_REFRESHED event - updating user');
      await userActions.setUser(session.user, session, false);
    } else {
      console.log('🔄 Unhandled auth event:', event);
    }

    console.log('🔄 Auth state after handling event:', authState.get());
  });

  // Clean up subscription on page unload
  window.addEventListener('beforeunload', () => {
    subscription.unsubscribe();
  });
}
