import type { LanguageModelV1 } from 'ai';
import type { IProviderSetting } from '~/types/model';

export interface ModelInfo {
  name: string;
  label: string;
  provider: string;
  maxTokenAllowed: number;
}

export interface ProviderInfo {
  name: string;
  staticModels: ModelInfo[];
  getDynamicModels?: (
    apiKeys?: Record<string, string>,
    settings?: IProviderSetting,
    serverEnv?: Record<string, string>,
  ) => Promise<ModelInfo[]>;
  getModelInstance: (options: {
    model: string;
    serverEnv: Env;
    apiKeys?: Record<string, string>;
    providerSettings?: Record<string, IProviderSetting>;
  }) => LanguageModelV1;
  getApiKeyLink?: string;
  labelForGetApiKey?: string;
  icon?: string;
}
export interface ProviderConfig {
  baseUrlKey?: string;
  baseUrl?: string;
  apiTokenKey?: string;
}

// Coding-optimized models for each provider
export const CODING_MODELS = {
  Google: [
    // Free tier models (available without billing)
    {
      name: 'gemini-2.0-flash',
      label: 'Gemini 2.0 Flash (Free)',
      provider: 'Google',
      maxTokenAllowed: 1000000,
      requiresBilling: false,
      description: 'Latest multimodal model, excellent for coding'
    },
    {
      name: 'gemini-1.5-flash',
      label: 'Gemini 1.5 Flash (Free)',
      provider: 'Google',
      maxTokenAllowed: 1000000,
      requiresBilling: false,
      description: 'Fast and efficient, great for most coding tasks'
    },
    // Paid tier models (require billing enabled)
    {
      name: 'gemini-2.5-flash-preview-05-20',
      label: 'Gemini 2.5 Flash Preview (Pro)',
      provider: 'Google',
      maxTokenAllowed: 1000000,
      requiresBilling: true,
      description: 'Advanced reasoning with thinking capabilities - requires billing'
    },
    {
      name: 'gemini-2.5-pro-preview-05-06',
      label: 'Gemini 2.5 Pro Preview (Pro)',
      provider: 'Google',
      maxTokenAllowed: 1000000,
      requiresBilling: true,
      description: 'Most powerful model for complex coding - requires billing'
    }
  ],
  OpenAI: [
    {
      name: 'gpt-4o',
      label: 'GPT-4o',
      provider: 'OpenAI',
      maxTokenAllowed: 128000,
      requiresBilling: true,
      description: 'Latest multimodal model, excellent for coding'
    },
    {
      name: 'gpt-4o-mini',
      label: 'GPT-4o Mini',
      provider: 'OpenAI',
      maxTokenAllowed: 128000,
      requiresBilling: true,
      description: 'Faster and more cost-effective'
    }
  ],
  Anthropic: [
    {
      name: 'claude-3-5-sonnet-20241022',
      label: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      maxTokenAllowed: 200000,
      requiresBilling: true,
      description: 'Excellent for coding and complex reasoning'
    },
    {
      name: 'claude-3-5-haiku-20241022',
      label: 'Claude 3.5 Haiku',
      provider: 'Anthropic',
      maxTokenAllowed: 200000,
      requiresBilling: true,
      description: 'Fast and efficient for coding tasks'
    }
  ]
} as const;

// Get coding models for a specific provider
export function getCodingModelsForProvider(provider: string, billingEnabled?: boolean): ModelInfo[] {
  const providerModels = CODING_MODELS[provider as keyof typeof CODING_MODELS];
  if (!providerModels) return [];

  // If billing status is unknown (undefined), show all models
  // Users will get appropriate error messages if they try to use paid models without billing
  if (billingEnabled === undefined) {
    return providerModels.map(model => ({
      name: model.name,
      label: model.label,
      provider: model.provider,
      maxTokenAllowed: model.maxTokenAllowed
    }));
  }

  // If billing status is known, filter accordingly
  return providerModels
    .filter(model => !model.requiresBilling || billingEnabled)
    .map(model => ({
      name: model.name,
      label: model.label,
      provider: model.provider,
      maxTokenAllowed: model.maxTokenAllowed
    }));
}

// Get recommended model for provider based on billing status
export function getRecommendedModel(provider: string, billingEnabled: boolean = false): string | null {
  const models = getCodingModelsForProvider(provider, billingEnabled);
  if (models.length === 0) return null;

  // Return the first (most recommended) model
  return models[0].name;
}
