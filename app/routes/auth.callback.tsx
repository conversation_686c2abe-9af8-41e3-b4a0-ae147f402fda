import { redirect, type LoaderFunctionArgs } from '@remix-run/cloudflare';
import { supabase } from '~/lib/supabase/client';
import { userActions } from '~/lib/stores/user';

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const error = url.searchParams.get('error');
  const errorDescription = url.searchParams.get('error_description');

  if (error) {
    console.error('Auth callback error:', error, errorDescription);
    return redirect(`/signin?error=${encodeURIComponent(errorDescription || error)}`);
  }

  if (code) {
    try {
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError);
        return redirect(`/signin?error=${encodeURIComponent(exchangeError.message)}`);
      }

      if (data.session && data.user) {
        console.log('🔄 AUTH_CALLBACK: OAuth successful, ensuring user profile exists...');

        try {
          // Ensure user profile exists immediately after OAuth
          await userActions.ensureUserProfile(data.user, data.session);
          console.log('✅ AUTH_CALLBACK: User profile ensured successfully');
        } catch (profileError) {
          console.error('⚠️ AUTH_CALLBACK: Profile creation failed, but continuing with login:', profileError);
          // Don't fail the login if profile creation fails - user can still use the app
        }

        // Redirect with success parameter to trigger toast
        return redirect('/?login=success');
      }
    } catch (error) {
      console.error('Auth callback error:', error);
      return redirect('/signin?error=Authentication failed');
    }
  }

  return redirect('/signin');
}
