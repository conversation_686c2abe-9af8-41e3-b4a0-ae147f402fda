import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import { Brain, Zap, Bot, Key, X, ExternalLink, Shield, Check, CheckCircle, Loader2, Save } from 'lucide-react';
import { useStore } from '@nanostores/react';
import { authState } from '~/lib/stores/user';
import { classNames } from '~/utils/classNames';
import { ApiKeyService, verifyApiKey, SUPPORTED_PROVIDERS } from '~/lib/services/apiKeyService';
import { supabase } from '~/lib/supabase/client';
import type { ProviderInfo } from '~/types/model';
import type { ModelInfo } from '~/lib/modules/llm/types';
import { getCodingModelsForProvider, getRecommendedModel } from '~/lib/modules/llm/types';

interface ProviderConfig {
  name: string;
  displayName: string;
  icon: string;
  getApiKeyUrl: string;
  description: string;
  models?: ModelInfo[];
}

interface ApiKeySetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
  title?: string;
  description?: string;
  selectedProvider?: ProviderInfo;
  modelList?: ModelInfo[];
  onProviderChange?: (provider: ProviderInfo) => void;
  onModelChange?: (model: string) => void;
  currentModel?: string;
}

// Icon mapping for Lucide React icons
const getProviderIcon = (iconName: string) => {
  switch (iconName) {
    case 'Brain':
      return Brain;
    case 'Zap':
      return Zap;
    case 'Bot':
      return Bot;
    default:
      return Brain;
  }
};

export function ApiKeySetupModal({
  isOpen,
  onClose,
  onComplete,
  title = '⚙️ AI Configuration',
  description = 'Configure your AI provider, model, and API keys',
  selectedProvider,
  modelList = [],
  onProviderChange,
  onModelChange,
  currentModel,
}: ApiKeySetupModalProps) {
  const auth = useStore(authState);
  const [internalSelectedProvider, setInternalSelectedProvider] = useState<string>('Google');
  const [apiKey, setApiKey] = useState<string>('');
  const [saving, setSaving] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [hasValidKey, setHasValidKey] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [verificationResult, setVerificationResult] = useState<{
    isValid: boolean;
    billingEnabled?: boolean;
    recommendedModel?: string;
  } | null>(null);
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [isKeyVerified, setIsKeyVerified] = useState(false);
  const [originalApiKey, setOriginalApiKey] = useState<string>(''); // Track original key to detect changes

  // Use external provider if provided, otherwise use internal state
  const activeProvider = selectedProvider?.name || internalSelectedProvider;

  useEffect(() => {
    if (isOpen && !auth.isAuthenticated) {
      // Redirect to signin page immediately if not authenticated
      window.location.href = '/signin';
      return;
    }

    if (isOpen && auth.isAuthenticated) {
      checkExistingKeys();
    }
  }, [isOpen, auth.isAuthenticated]);

  // Initialize selected model when provider changes
  useEffect(() => {
    if (currentModel) {
      setSelectedModel(currentModel);
    } else if (availableModels.length > 0) {
      setSelectedModel(availableModels[0].name);
    }
  }, [activeProvider, availableModels, currentModel]);

  // Handle provider change
  const handleProviderChange = async (providerName: string) => {
    if (selectedProvider && onProviderChange) {
      // External provider management
      const newProvider = { name: providerName } as ProviderInfo;
      onProviderChange(newProvider);

      // Auto-select first model for the new provider
      const newProviderModels = modelList.filter((model) => model.provider === providerName);
      if (newProviderModels.length > 0 && onModelChange) {
        onModelChange(newProviderModels[0].name);
      }
    } else {
      // Internal provider management
      setInternalSelectedProvider(providerName);
    }

    // Reset verification state when switching providers
    if (activeProvider !== providerName) {
      setApiKey('');
      setOriginalApiKey('');
      setValidationError(null);
      setVerificationResult(null);
      setShowModelSelector(false);
      setAvailableModels([]);
      setIsKeyVerified(false);

      // Load existing API key for the new provider
      if (auth.user) {
        await loadExistingApiKey(auth.user.id, providerName);
      }
    }
  };

  // Handle model change with auto-save for verified keys
  const handleModelChange = async (modelName: string) => {
    setSelectedModel(modelName);
    if (onModelChange) {
      onModelChange(modelName);
    }

    // If key is already verified and saved, auto-update the model selection
    if (isKeyVerified && verificationResult?.isValid && auth.user) {
      try {
        // Save the updated model selection
        const keyToSave = typeof apiKey === 'string' ? apiKey.trim() : '';
        await ApiKeyService.saveApiKey(supabase, auth.user.id, activeProvider, keyToSave);

        // Sync to cookies for compatibility
        await ApiKeyService.syncApiKeysToCookies(supabase, auth.user.id);

        toast.success(`Model switched to ${modelName}`, {
          position: 'bottom-right',
          autoClose: 2000,
        });
      } catch (error) {
        console.error('Error updating model selection:', error);
      }
    }
  };

  const checkExistingKeys = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) return;

      const hasKeys = await ApiKeyService.hasApiKeys(supabase, user.id);
      setHasValidKey(hasKeys);

      // Load existing API key for current provider
      await loadExistingApiKey(user.id, activeProvider);
    } catch (error) {
      console.error('Error checking existing keys:', error);
    }
  };

  const loadExistingApiKey = async (userId: string, providerName: string) => {
    try {
      const existingKey = await ApiKeyService.getApiKey(supabase, userId, providerName);

      console.log('🔍 Loading existing API key debug:', {
        provider: providerName,
        keyType: typeof existingKey,
        keyValue:
          existingKey && typeof existingKey === 'string' && existingKey.length > 8
            ? `${existingKey.substring(0, 8)}...`
            : existingKey,
        isString: typeof existingKey === 'string',
        keyLength: existingKey ? (typeof existingKey === 'string' ? existingKey.length : 'not string') : 'null',
      });

      if (existingKey && typeof existingKey === 'string' && existingKey.trim().length > 0) {
        setApiKey(existingKey);
        setOriginalApiKey(existingKey);
        // Auto-verify the existing key to show models (only if key is not empty)
        if (existingKey.trim().length >= 10) {
          await handleVerifyKey(existingKey);
          setIsKeyVerified(true);
        }
      } else {
        // No existing key, reset states
        setApiKey('');
        setOriginalApiKey('');
        setIsKeyVerified(false);
        setVerificationResult(null);
        setShowModelSelector(false);
      }
    } catch (error) {
      console.error('Error loading existing API key:', error);
      setApiKey('');
      setOriginalApiKey('');
      setIsKeyVerified(false);
    }
  };

  const handleSave = async () => {
    if (!verificationResult?.isValid) {
      setValidationError('Please verify your API key first');
      return;
    }

    if (!selectedModel) {
      setValidationError('Please select a model');
      return;
    }

    if (!auth.isAuthenticated) {
      toast.error('Please sign in to save API keys', {
        position: 'bottom-right',
      });
      return;
    }

    try {
      setSaving(true);
      setValidationError(null);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        setValidationError('Authentication required');
        return;
      }

      // Save directly using the service with client-side Supabase
      const keyToSave = typeof apiKey === 'string' ? apiKey.trim() : '';
      await ApiKeyService.saveApiKey(supabase, user.id, activeProvider, keyToSave);

      // Sync to cookies for compatibility with existing system
      await ApiKeyService.syncApiKeysToCookies(supabase, user.id);

      toast.success(`${activeProvider} API key saved with ${selectedModel}!`, {
        position: 'bottom-right',
      });
      setApiKey('');
      setHasValidKey(true);
      onComplete?.();
      onClose();
    } catch (error) {
      console.error('Error saving API key:', error);
      setValidationError(error instanceof Error ? error.message : 'Failed to save API key');
    } finally {
      setSaving(false);
    }
  };

  const handleApiKeyChange = (value: string) => {
    console.log('🔑 API Key input changed:', {
      valueType: typeof value,
      valueLength: value ? value.length : 0,
      valuePreview: value && value.length > 0 ? `${value.substring(0, 8)}...` : 'empty',
      currentApiKeyState: apiKey,
      currentApiKeyLength: apiKey ? apiKey.length : 0,
    });
    setApiKey(value);

    // Check if the key has changed from the original
    const hasKeyChanged = value !== originalApiKey;

    if (hasKeyChanged) {
      // Key changed, need to re-verify
      setIsKeyVerified(false);
      setValidationError(null);
      setVerificationResult(null);
      setShowModelSelector(false);
    } else if (value === originalApiKey && originalApiKey) {
      // Key is back to original and was previously verified
      setIsKeyVerified(true);
      // Re-verify to restore model selector
      if (value.trim()) {
        handleVerifyKey(value);
      }
    }
  };

  const handleVerifyKey = async (keyToVerify?: string) => {
    // Enhanced type handling to ensure we always have a string
    let keyValue: string;

    if (keyToVerify && typeof keyToVerify === 'string') {
      keyValue = keyToVerify.trim();
    } else if (typeof apiKey === 'string') {
      keyValue = apiKey.trim();
    } else {
      keyValue = '';
    }

    console.log('🔍 HandleVerifyKey Debug:', {
      provider: activeProvider,
      keyToVerify: keyToVerify && typeof keyToVerify === 'string' ? `${keyToVerify.substring(0, 8)}...` : 'undefined',
      apiKeyState:
        typeof apiKey === 'string' && apiKey.length > 0 ? `${apiKey.substring(0, 8)}...` : `type: ${typeof apiKey}`,
      keyValue: keyValue && keyValue.length > 0 ? `${keyValue.substring(0, 8)}...` : 'empty',
      keyValueType: typeof keyValue,
      keyValueLength: keyValue.length,
      processedCorrectly: typeof keyValue === 'string',
    });

    // Now we can safely use keyValue as a string
    if (!keyValue || keyValue.length === 0) {
      setValidationError('Please enter an API key');
      return;
    }

    if (keyValue.length < 10) {
      setValidationError('API key appears to be too short (minimum 10 characters)');
      return;
    }

    try {
      setVerifying(true);
      setValidationError(null);

      const result = await verifyApiKey(activeProvider, keyValue);
      setVerificationResult(result);

      if (result.isValid) {
        // Get available models based on billing status
        const models = getCodingModelsForProvider(activeProvider, result.billingEnabled);
        setAvailableModels(models);

        // Set recommended model
        if (result.recommendedModel) {
          setSelectedModel(result.recommendedModel);
          if (onModelChange) {
            onModelChange(result.recommendedModel);
          }
        }

        setShowModelSelector(true);
        setIsKeyVerified(true);

        // Update original key if this was a new verification
        if (!keyToVerify) {
          setOriginalApiKey(keyValue);
        }

        // Only show toast for manual verification, not auto-verification
        if (!keyToVerify) {
          toast.success(`✅ API key verified! ${availableModels.length} models available`, {
            position: 'bottom-right',
          });
        }
      } else {
        setValidationError(result.error || 'API key verification failed');
        setShowModelSelector(false);
        setIsKeyVerified(false);
      }
    } catch (error) {
      console.error('Error verifying API key:', error);
      setValidationError(error instanceof Error ? error.message : 'Verification failed');
      setShowModelSelector(false);
      setIsKeyVerified(false);
    } finally {
      setVerifying(false);
    }
  };

  const handleSkip = () => {
    toast.info('You can add API keys later in the settings.', {
      position: 'bottom-right',
      autoClose: 4000,
    });
    onClose();
  };

  const selectedProviderConfig = SUPPORTED_PROVIDERS.find((p) => p.name === activeProvider);

  // Don't render modal if user is not authenticated
  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="bg-gradient-to-br from-bolt-elements-background-depth-1 via-bolt-elements-background-depth-1 to-bolt-elements-background-depth-2 rounded-2xl shadow-2xl max-w-3xl w-full max-h-[85vh] border border-blue-500/20 flex flex-col"
          >
            {/* Header */}
            <div className="p-6 border-b border-blue-500/20 bg-gradient-to-r from-blue-500/5 to-purple-600/5">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Key className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-bolt-elements-textPrimary">{title}</h2>
                    <p className="text-sm text-bolt-elements-textSecondary mt-1">{description}</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-600/10 rounded-lg transition-all duration-300 hover:scale-105"
                >
                  <X className="w-5 h-5 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-6 space-y-6">
                <div className="space-y-6">
                  {/* Provider Selection */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-bolt-elements-textPrimary">Choose AI Provider</h3>
                    <div className="grid grid-cols-1 gap-3">
                      {SUPPORTED_PROVIDERS.map((provider) => (
                        <button
                          key={provider.name}
                          onClick={() => handleProviderChange(provider.name)}
                          className={classNames(
                            'p-4 rounded-xl border-2 transition-all duration-300 text-left hover:scale-[1.02]',
                            activeProvider === provider.name
                              ? 'border-blue-500 bg-gradient-to-r from-blue-500/10 to-purple-600/10 shadow-lg'
                              : 'border-bolt-elements-borderColor hover:border-blue-500/50 hover:bg-gradient-to-r hover:from-blue-500/5 hover:to-purple-600/5',
                          )}
                        >
                          <div className="flex items-center gap-4">
                            {React.createElement(getProviderIcon(provider.icon), {
                              className: 'w-8 h-8 text-bolt-elements-textPrimary',
                            })}
                            <div className="flex-1">
                              <div className="font-medium text-bolt-elements-textPrimary">{provider.displayName}</div>
                              <div className="text-sm text-bolt-elements-textSecondary">{provider.description}</div>
                            </div>
                            {activeProvider === provider.name && <CheckCircle className="w-5 h-5 text-blue-500" />}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* API Key Input */}
                  {selectedProviderConfig && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-bolt-elements-textPrimary">
                          {selectedProviderConfig.displayName} API Key
                        </h3>
                        <a
                          href={selectedProviderConfig.getApiKeyUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-500 hover:text-blue-400 flex items-center gap-1 hover:underline"
                        >
                          Get API Key
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>

                      <div className="space-y-3">
                        <div className="relative">
                          <input
                            type="password"
                            value={apiKey}
                            onChange={(e) => handleApiKeyChange(e.target.value)}
                            placeholder={`Enter your ${selectedProviderConfig.displayName} API key...`}
                            className="w-full px-4 py-3 bg-bolt-elements-background-depth-2 border border-bolt-elements-borderColor rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-bolt-elements-textPrimary placeholder-bolt-elements-textTertiary"
                          />
                        </div>

                        <button
                          onClick={handleVerifyKey}
                          disabled={!(typeof apiKey === 'string' && apiKey.trim()) || verifying || isKeyVerified}
                          className={`w-full px-4 py-3 rounded-lg font-medium transition-all flex items-center justify-center gap-2 ${
                            isKeyVerified
                              ? 'bg-green-500/20 border border-green-500/30 text-green-400 cursor-not-allowed'
                              : 'genvibe-button-primary disabled:opacity-50 disabled:cursor-not-allowed hover:opacity-90'
                          }`}
                        >
                          {verifying ? (
                            <>
                              <Loader2 className="w-4 h-4 animate-spin" />
                              Verifying...
                            </>
                          ) : isKeyVerified ? (
                            <>
                              <CheckCircle className="w-4 h-4" />
                              Key Verified
                            </>
                          ) : (
                            <>
                              <Shield className="w-4 h-4" />
                              Verify API Key
                            </>
                          )}
                        </button>

                        {validationError && (
                          <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                            <p className="text-sm text-red-400">{validationError}</p>
                          </div>
                        )}

                        {verificationResult?.isValid && (
                          <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                            <p className="text-sm text-green-400">✅ API key verified successfully!</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Model Selection */}
                  {showModelSelector && availableModels.length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-bolt-elements-textPrimary">Select Model</h3>
                        {isKeyVerified && (
                          <span className="text-xs text-bolt-elements-textTertiary bg-bolt-elements-background-depth-2 px-2 py-1 rounded">
                            Auto-saves on change
                          </span>
                        )}
                      </div>
                      <div className="space-y-2">
                        {availableModels.map((model) => (
                          <button
                            key={model.name}
                            onClick={() => handleModelChange(model.name)}
                            className={classNames(
                              'w-full p-3 rounded-lg border transition-all duration-200 text-left hover:scale-[1.01]',
                              selectedModel === model.name
                                ? 'border-blue-500 bg-gradient-to-r from-blue-500/10 to-purple-600/10 text-bolt-elements-textPrimary shadow-lg'
                                : 'border-bolt-elements-borderColor hover:border-blue-500/50 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary hover:bg-gradient-to-r hover:from-blue-500/5 hover:to-purple-600/5',
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium">{model.label}</div>
                                {model.description && (
                                  <div className="text-xs text-bolt-elements-textTertiary mt-1">
                                    {model.description}
                                  </div>
                                )}
                              </div>
                              {selectedModel === model.name && <CheckCircle className="w-5 h-5 text-blue-500" />}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-auto p-6 border-t border-blue-500/20 bg-gradient-to-r from-bolt-elements-background-depth-2 to-bolt-elements-background-depth-1">
              <div className="space-y-4">
                <div className="flex gap-3">
                  <button
                    onClick={handleSkip}
                    className="flex-1 px-4 py-3 bg-bolt-elements-background-depth-2 hover:bg-bolt-elements-background-depth-3 text-bolt-elements-textSecondary hover:text-bolt-elements-textPrimary border border-bolt-elements-borderColor rounded-lg font-medium transition-all"
                  >
                    Skip for Now
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={!verificationResult?.isValid || !selectedModel || saving}
                    className="flex-1 genvibe-button-primary px-4 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:opacity-90 transition-all flex items-center justify-center gap-2"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Saving...
                      </>
                    ) : isKeyVerified && apiKey === originalApiKey ? (
                      <>
                        <Check className="w-4 h-4" />
                        Continue
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Save & Continue
                      </>
                    )}
                  </button>
                </div>

                <div className="text-center">
                  <p className="text-xs text-bolt-elements-textTertiary">
                    Your API keys are securely stored and encrypted. You can manage them in settings.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
