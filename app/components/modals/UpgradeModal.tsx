import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Check, Crown, Zap, Shield, Headphones, Sparkles, ArrowRight } from 'lucide-react';
import { classNames } from '~/utils/classNames';
import { toast } from 'react-toastify';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface PlanFeature {
  text: string;
  included: boolean;
}

interface Plan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  buttonText: string;
  buttonAction: () => void;
}

export function UpgradeModal({ isOpen, onClose }: UpgradeModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement actual upgrade logic
      toast.success('Redirecting to payment...');
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      toast.error('Failed to process upgrade');
    } finally {
      setIsLoading(false);
    }
  };

  const plans: Plan[] = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for trying out GenVibe',
      features: [
        { text: '10 messages per day', included: true },
        { text: 'Basic AI models', included: true },
        { text: 'Standard support', included: true },
        { text: 'Export projects', included: true },
        { text: 'Unlimited messages', included: false },
        { text: 'Premium AI models', included: false },
        { text: 'Priority support', included: false },
        { text: 'Early access features', included: false },
      ],
      buttonText: 'Current Plan',
      buttonAction: () => {},
    },
    {
      name: 'Pro',
      price: '$20',
      period: 'month',
      description: 'For serious developers and creators',
      popular: true,
      features: [
        { text: 'Unlimited messages', included: true },
        { text: 'All AI models (GPT-4, Claude, etc.)', included: true },
        { text: 'Priority support', included: true },
        { text: 'Export projects', included: true },
        { text: 'Early access features', included: true },
        { text: 'Custom templates', included: true },
        { text: 'Advanced code generation', included: true },
        { text: 'API access', included: true },
      ],
      buttonText: 'Upgrade to Pro',
      buttonAction: handleUpgrade,
    },
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 text-white p-6">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>

            <div className="text-center">
              <div className="flex items-center justify-center gap-3 mb-3">
                <div className="p-2 bg-white/10 rounded-xl">
                  <Crown size={24} className="text-yellow-300" />
                </div>
                <h1 className="text-2xl font-bold">Upgrade to GenVibe Pro</h1>
              </div>
              <p className="text-blue-100 mb-4">Unlock unlimited creativity with premium features</p>

              {/* Benefits highlights */}
              <div className="flex flex-wrap justify-center gap-3 text-sm">
                {[
                  { icon: Zap, text: 'Unlimited Messages' },
                  { icon: Sparkles, text: 'Premium AI Models' },
                  { icon: Shield, text: 'Priority Support' },
                  { icon: Headphones, text: 'Early Access' },
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2 bg-white/10 px-3 py-1.5 rounded-full">
                    <benefit.icon size={14} />
                    <span>{benefit.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Plans comparison */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {plans.map((plan, index) => (
                <motion.div
                  key={plan.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={classNames(
                    'relative rounded-xl border-2 p-6 transition-all duration-300',
                    plan.popular
                      ? 'border-blue-500 bg-gradient-to-b from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 shadow-xl'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600',
                  )}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg">
                        Most Popular
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
                    <div className="flex items-baseline justify-center gap-1 mb-2">
                      <span className="text-3xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                      <span className="text-gray-500 dark:text-gray-400">/{plan.period}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{plan.description}</p>
                  </div>

                  <div className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3">
                        <div
                          className={classNames(
                            'flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center',
                            feature.included ? 'bg-green-100 dark:bg-green-900/30' : 'bg-gray-100 dark:bg-gray-800',
                          )}
                        >
                          {feature.included ? (
                            <Check size={12} className="text-green-600 dark:text-green-400" />
                          ) : (
                            <X size={12} className="text-gray-400" />
                          )}
                        </div>
                        <span
                          className={classNames(
                            'text-sm',
                            feature.included ? 'text-gray-900 dark:text-white' : 'text-gray-400 dark:text-gray-500',
                          )}
                        >
                          {feature.text}
                        </span>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={plan.buttonAction}
                    disabled={plan.name === 'Free' || isLoading}
                    className={classNames(
                      'w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center gap-2',
                      plan.popular
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                        : plan.name === 'Free'
                          ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                          : 'bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100',
                    )}
                  >
                    {plan.buttonText}
                    {plan.popular && <ArrowRight size={16} />}
                  </button>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-4">
            <div className="text-center">
              <h4 className="font-medium text-gray-900 dark:text-white mb-1 text-sm">🎯 30-Day Money-Back Guarantee</h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Try GenVibe Pro risk-free. Not satisfied? Get a full refund within 30 days.
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
