/* Color Tokens Light Theme */
:root,
:root[data-theme='light'] {
  --bolt-elements-borderColor: theme('colors.alpha.gray.10');
  --bolt-elements-borderColorActive: theme('colors.accent.600');

  --bolt-elements-bg-depth-1: theme('colors.white');
  --bolt-elements-bg-depth-2: theme('colors.gray.50');
  --bolt-elements-bg-depth-3: theme('colors.gray.200');
  --bolt-elements-bg-depth-4: theme('colors.alpha.gray.5');

  --bolt-elements-textPrimary: theme('colors.gray.950');
  --bolt-elements-textSecondary: theme('colors.gray.600');
  --bolt-elements-textTertiary: theme('colors.gray.500');

  --bolt-elements-code-background: theme('colors.gray.100');
  --bolt-elements-code-text: theme('colors.gray.950');

  --bolt-elements-button-primary-background: theme('colors.alpha.accent.10');
  --bolt-elements-button-primary-backgroundHover: theme('colors.alpha.accent.20');
  --bolt-elements-button-primary-text: theme('colors.accent.500');

  --bolt-elements-button-secondary-background: theme('colors.alpha.gray.5');
  --bolt-elements-button-secondary-backgroundHover: theme('colors.alpha.gray.10');
  --bolt-elements-button-secondary-text: theme('colors.gray.950');

  --bolt-elements-button-danger-background: theme('colors.alpha.red.10');
  --bolt-elements-button-danger-backgroundHover: theme('colors.alpha.red.20');
  --bolt-elements-button-danger-text: theme('colors.red.500');

  --bolt-elements-item-contentDefault: theme('colors.alpha.gray.50');
  --bolt-elements-item-contentActive: theme('colors.gray.950');
  --bolt-elements-item-contentAccent: theme('colors.accent.700');
  --bolt-elements-item-contentDanger: theme('colors.red.500');
  --bolt-elements-item-backgroundDefault: rgba(0, 0, 0, 0);
  --bolt-elements-item-backgroundActive: theme('colors.alpha.gray.5');
  --bolt-elements-item-backgroundAccent: theme('colors.alpha.accent.10');
  --bolt-elements-item-backgroundDanger: theme('colors.alpha.red.10');

  --bolt-elements-loader-background: theme('colors.alpha.gray.10');
  --bolt-elements-loader-progress: theme('colors.accent.500');

  --bolt-elements-artifacts-background: theme('colors.white');
  --bolt-elements-artifacts-backgroundHover: theme('colors.alpha.gray.2');
  --bolt-elements-artifacts-borderColor: var(--bolt-elements-borderColor);
  --bolt-elements-artifacts-inlineCode-background: theme('colors.gray.100');
  --bolt-elements-artifacts-inlineCode-text: var(--bolt-elements-textPrimary);

  --bolt-elements-actions-background: theme('colors.white');
  --bolt-elements-actions-code-background: theme('colors.gray.800');

  --bolt-elements-messages-background: theme('colors.gray.100');
  --bolt-elements-messages-linkColor: theme('colors.accent.500');
  --bolt-elements-messages-code-background: theme('colors.gray.800');
  --bolt-elements-messages-inlineCode-background: theme('colors.gray.200');
  --bolt-elements-messages-inlineCode-text: theme('colors.gray.800');

  --bolt-elements-icon-success: theme('colors.green.500');
  --bolt-elements-icon-error: theme('colors.red.500');
  --bolt-elements-icon-primary: theme('colors.gray.950');
  --bolt-elements-icon-secondary: theme('colors.gray.600');
  --bolt-elements-icon-tertiary: theme('colors.gray.500');

  --bolt-elements-dividerColor: theme('colors.gray.100');

  --bolt-elements-prompt-background: theme('colors.alpha.white.80');

  --bolt-elements-sidebar-dropdownShadow: theme('colors.alpha.gray.10');
  --bolt-elements-sidebar-buttonBackgroundDefault: theme('colors.alpha.accent.10');
  --bolt-elements-sidebar-buttonBackgroundHover: theme('colors.alpha.accent.20');
  --bolt-elements-sidebar-buttonText: theme('colors.accent.700');

  --bolt-elements-preview-addressBar-background: theme('colors.gray.100');
  --bolt-elements-preview-addressBar-backgroundHover: theme('colors.alpha.gray.5');
  --bolt-elements-preview-addressBar-backgroundActive: theme('colors.white');
  --bolt-elements-preview-addressBar-text: var(--bolt-elements-textSecondary);
  --bolt-elements-preview-addressBar-textActive: var(--bolt-elements-textPrimary);

  --bolt-elements-terminals-background: theme('colors.white');
  --bolt-elements-terminals-buttonBackground: var(--bolt-elements-bg-depth-4);

  --bolt-elements-cta-background: theme('colors.gray.100');
  --bolt-elements-cta-text: theme('colors.gray.950');

  /* Terminal Colors */
  --bolt-terminal-background: var(--bolt-elements-terminals-background);
  --bolt-terminal-foreground: #333333;
  --bolt-terminal-selection-background: #00000040;
  --bolt-terminal-black: #000000;
  --bolt-terminal-red: #cd3131;
  --bolt-terminal-green: #00bc00;
  --bolt-terminal-yellow: #949800;
  --bolt-terminal-blue: #0451a5;
  --bolt-terminal-magenta: #bc05bc;
  --bolt-terminal-cyan: #0598bc;
  --bolt-terminal-white: #555555;
  --bolt-terminal-brightBlack: #686868;
  --bolt-terminal-brightRed: #cd3131;
  --bolt-terminal-brightGreen: #00bc00;
  --bolt-terminal-brightYellow: #949800;
  --bolt-terminal-brightBlue: #0451a5;
  --bolt-terminal-brightMagenta: #bc05bc;
  --bolt-terminal-brightCyan: #0598bc;
  --bolt-terminal-brightWhite: #a5a5a5;
  --modern-scrollbar-thumb-background: rgba(100, 100, 100, 0.3); // Example light theme color
  --modern-scrollbar-thumb-backgroundHover: rgba(74, 74, 74, 0.8);
}

/* Color Tokens Dark Theme */
:root,
:root[data-theme='dark'] {
  --bolt-elements-borderColor: theme('colors.alpha.white.10');
  --bolt-elements-borderColorActive: theme('colors.accent.500');

  --bolt-elements-bg-depth-1: theme('colors.gray.950');
  --bolt-elements-bg-depth-2: theme('colors.gray.900');
  --bolt-elements-bg-depth-3: theme('colors.gray.800');
  --bolt-elements-bg-depth-4: theme('colors.alpha.white.5');

  --bolt-elements-textPrimary: theme('colors.white');
  --bolt-elements-textSecondary: theme('colors.gray.400');
  --bolt-elements-textTertiary: theme('colors.gray.500');

  --bolt-elements-code-background: theme('colors.gray.800');
  --bolt-elements-code-text: theme('colors.white');

  --bolt-elements-button-primary-background: theme('colors.alpha.accent.10');
  --bolt-elements-button-primary-backgroundHover: theme('colors.alpha.accent.20');
  --bolt-elements-button-primary-text: theme('colors.accent.500');

  --bolt-elements-button-secondary-background: theme('colors.alpha.white.5');
  --bolt-elements-button-secondary-backgroundHover: theme('colors.alpha.white.10');
  --bolt-elements-button-secondary-text: theme('colors.white');

  --bolt-elements-button-danger-background: theme('colors.alpha.red.10');
  --bolt-elements-button-danger-backgroundHover: theme('colors.alpha.red.20');
  --bolt-elements-button-danger-text: theme('colors.red.500');

  --bolt-elements-item-contentDefault: theme('colors.alpha.white.50');
  --bolt-elements-item-contentActive: theme('colors.white');
  --bolt-elements-item-contentAccent: theme('colors.accent.500');
  --bolt-elements-item-contentDanger: theme('colors.red.500');
  --bolt-elements-item-backgroundDefault: rgba(255, 255, 255, 0);
  --bolt-elements-item-backgroundActive: theme('colors.alpha.white.10');
  --bolt-elements-item-backgroundAccent: theme('colors.alpha.accent.10');
  --bolt-elements-item-backgroundDanger: theme('colors.alpha.red.10');

  --bolt-elements-loader-background: theme('colors.alpha.gray.10');
  --bolt-elements-loader-progress: theme('colors.accent.500');

  --bolt-elements-artifacts-background: theme('colors.gray.900');
  --bolt-elements-artifacts-backgroundHover: theme('colors.alpha.white.5');
  --bolt-elements-artifacts-borderColor: var(--bolt-elements-borderColor);
  --bolt-elements-artifacts-inlineCode-background: theme('colors.gray.800');
  --bolt-elements-artifacts-inlineCode-text: theme('colors.white');

  --bolt-elements-actions-background: theme('colors.gray.900');
  --bolt-elements-actions-code-background: theme('colors.gray.800');

  --bolt-elements-messages-background: theme('colors.gray.800');
  --bolt-elements-messages-linkColor: theme('colors.accent.500');
  --bolt-elements-messages-code-background: theme('colors.gray.900');
  --bolt-elements-messages-inlineCode-background: theme('colors.gray.700');
  --bolt-elements-messages-inlineCode-text: var(--bolt-elements-textPrimary);

  --bolt-elements-icon-success: theme('colors.green.400');
  --bolt-elements-icon-error: theme('colors.red.400');
  --bolt-elements-icon-primary: theme('colors.gray.950');
  --bolt-elements-icon-secondary: theme('colors.gray.600');
  --bolt-elements-icon-tertiary: theme('colors.gray.500');

  --bolt-elements-dividerColor: theme('colors.gray.100');

  --bolt-elements-prompt-background: theme('colors.alpha.gray.80');

  --bolt-elements-sidebar-dropdownShadow: theme('colors.alpha.gray.30');
  --bolt-elements-sidebar-buttonBackgroundDefault: theme('colors.alpha.accent.10');
  --bolt-elements-sidebar-buttonBackgroundHover: theme('colors.alpha.accent.20');
  --bolt-elements-sidebar-buttonText: theme('colors.accent.500');

  --bolt-elements-preview-addressBar-background: var(--bolt-elements-bg-depth-1);
  --bolt-elements-preview-addressBar-backgroundHover: theme('colors.alpha.white.5');
  --bolt-elements-preview-addressBar-backgroundActive: var(--bolt-elements-bg-depth-1);
  --bolt-elements-preview-addressBar-text: var(--bolt-elements-textSecondary);
  --bolt-elements-preview-addressBar-textActive: var(--bolt-elements-textPrimary);

  --bolt-elements-terminals-background: var(--bolt-elements-bg-depth-1);
  --bolt-elements-terminals-buttonBackground: var(--bolt-elements-bg-depth-3);

  --bolt-elements-cta-background: theme('colors.alpha.white.10');
  --bolt-elements-cta-text: theme('colors.white');

  /* Terminal Colors */
  --bolt-terminal-background: var(--bolt-elements-terminals-background);
  --bolt-terminal-foreground: #eff0eb;
  --bolt-terminal-selection-background: #97979b33;
  --bolt-terminal-black: #000000;
  --bolt-terminal-red: #ff5c57;
  --bolt-terminal-green: #5af78e;
  --bolt-terminal-yellow: #f3f99d;
  --bolt-terminal-blue: #57c7ff;
  --bolt-terminal-magenta: #ff6ac1;
  --bolt-terminal-cyan: #9aedfe;
  --bolt-terminal-white: #f1f1f0;
  --bolt-terminal-brightBlack: #686868;
  --bolt-terminal-brightRed: #ff5c57;
  --bolt-terminal-brightGreen: #5af78e;
  --bolt-terminal-brightYellow: #f3f99d;
  --bolt-terminal-brightBlue: #57c7ff;
  --bolt-terminal-brightMagenta: #ff6ac1;
  --bolt-terminal-brightCyan: #9aedfe;
  --bolt-terminal-brightWhite: #f1f1f0;
  --modern-scrollbar-thumb-background: rgba(100, 100, 100, 0.3); // Example dark theme color (adjust as needed)
  --modern-scrollbar-thumb-backgroundHover: rgba(10, 10, 10, 0.8);
}

/*
 * Element Tokens
 *
 * Hierarchy: Element Token -> (Element Token | Color Tokens) -> Primitives
 */
:root {
  --header-height: 54px;
  --chat-max-width: 35rem;
  --chat-min-width: 575px;
  --workbench-width: min(calc(100% - var(--chat-min-width)), 2536px);
  --workbench-inner-width: var(--workbench-width);
  --workbench-left: calc(100% - var(--workbench-width));

  /* Toasts */
  --toastify-color-progress-success: var(--bolt-elements-icon-success);
  --toastify-color-progress-error: var(--bolt-elements-icon-error);

  /* Terminal */
  --bolt-elements-terminal-backgroundColor: var(--bolt-terminal-background);
  --bolt-elements-terminal-textColor: var(--bolt-terminal-foreground);
  --bolt-elements-terminal-cursorColor: var(--bolt-terminal-foreground);
  --bolt-elements-terminal-selection-backgroundColor: var(--bolt-terminal-selection-background);
  --bolt-elements-terminal-color-black: var(--bolt-terminal-black);
  --bolt-elements-terminal-color-red: var(--bolt-terminal-red);
  --bolt-elements-terminal-color-green: var(--bolt-terminal-green);
  --bolt-elements-terminal-color-yellow: var(--bolt-terminal-yellow);
  --bolt-elements-terminal-color-blue: var(--bolt-terminal-blue);
  --bolt-elements-terminal-color-magenta: var(--bolt-terminal-magenta);
  --bolt-elements-terminal-color-cyan: var(--bolt-terminal-cyan);
  --bolt-elements-terminal-color-white: var(--bolt-terminal-white);
  --bolt-elements-terminal-color-brightBlack: var(--bolt-terminal-brightBlack);
  --bolt-elements-terminal-color-brightRed: var(--bolt-terminal-brightRed);
  --bolt-elements-terminal-color-brightGreen: var(--bolt-terminal-brightGreen);
  --bolt-elements-terminal-color-brightYellow: var(--bolt-terminal-brightYellow);
  --bolt-elements-terminal-color-brightBlue: var(--bolt-terminal-brightBlue);
  --bolt-elements-terminal-color-brightMagenta: var(--bolt-terminal-brightMagenta);
  --bolt-elements-terminal-color-brightCyan: var(--bolt-terminal-brightCyan);
  --bolt-elements-terminal-color-brightWhite: var(--bolt-terminal-brightWhite);
}
