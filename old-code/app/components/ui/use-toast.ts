import { useCallback } from 'react';
import { toast as toastify } from 'react-toastify';

// Configure standard toast settings with modern styling
export const configuredToast = {
  success: (message: string, options = {}) => toastify.success(message, {
    autoClose: 3000,
    className: 'modern-toast modern-toast-success',
    bodyClassName: 'modern-toast-body',
    position: 'bottom-right',
    ...options
  }),
  error: (message: string, options = {}) => toastify.error(message, {
    autoClose: 3000,
    className: 'modern-toast modern-toast-error',
    bodyClassName: 'modern-toast-body',
    position: 'bottom-right',
    ...options
  }),
  info: (message: string, options = {}) => toastify.info(message, {
    autoClose: 3000,
    className: 'modern-toast modern-toast-info',
    bodyClassName: 'modern-toast-body',
    position: 'bottom-right',
    ...options
  }),
  warning: (message: string, options = {}) => toastify.warning(message, {
    autoClose: 3000,
    className: 'modern-toast modern-toast-warning',
    bodyClassName: 'modern-toast-body',
    position: 'bottom-right',
    ...options
  }),
  loading: (message: string, options = {}) => toastify.loading(message, {
    autoClose: 3000,
    className: 'modern-toast',
    bodyClassName: 'modern-toast-body',
    position: 'bottom-right',
    ...options
  }),
};

// Export the original toast for cases where specific configuration is needed
export { toastify as toast };

interface ToastOptions {
  type?: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
}

export function useToast() {
  const toast = useCallback((message: string, options: ToastOptions = {}) => {
    const { type = 'info', duration = 3000 } = options;

    const getModernClassName = (toastType: string) => {
      switch (toastType) {
        case 'success': return 'modern-toast modern-toast-success';
        case 'error': return 'modern-toast modern-toast-error';
        case 'warning': return 'modern-toast modern-toast-warning';
        case 'info': return 'modern-toast modern-toast-info';
        default: return 'modern-toast';
      }
    };

    toastify[type](message, {
      position: 'bottom-right',
      autoClose: duration,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: 'dark',
      className: getModernClassName(type),
      bodyClassName: 'modern-toast-body',
    });
  }, []);

  const success = useCallback(
    (message: string, options: Omit<ToastOptions, 'type'> = {}) => {
      toast(message, { ...options, type: 'success' });
    },
    [toast],
  );

  const error = useCallback(
    (message: string, options: Omit<ToastOptions, 'type'> = {}) => {
      toast(message, { ...options, type: 'error' });
    },
    [toast],
  );

  const info = useCallback(
    (message: string, options: Omit<ToastOptions, 'type'> = {}) => {
      toast(message, { ...options, type: 'info' });
    },
    [toast],
  );

  const warning = useCallback(
    (message: string, options: Omit<ToastOptions, 'type'> = {}) => {
      toast(message, { ...options, type: 'warning' });
    },
    [toast],
  );

  return { toast, success, error, info, warning };
}
