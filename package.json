{"name": "bolt", "description": "An AI Agent", "private": true, "license": "MIT", "sideEffects": false, "type": "module", "version": "1.0.0", "author": {"name": "bolt.diy team", "email": "<EMAIL>"}, "scripts": {"deploy": "npm run build && wrangler pages deploy", "build": "remix vite:build", "dev": "node pre-start.cjs  && remix vite:dev", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint app", "lint:fix": "npm run lint -- --fix && prettier app --write", "start:windows": "wrangler pages dev ./build/client", "start:unix": "bindings=$(./bindings.sh) && wrangler pages dev ./build/client $bindings", "start": "node -e \"const { spawn } = require('child_process'); const isWindows = process.platform === 'win32'; const cmd = isWindows ? 'npm run start:windows' : 'npm run start:unix'; const child = spawn(cmd, { shell: true, stdio: 'inherit' }); child.on('exit', code => process.exit(code));\"", "dockerstart": "bindings=$(./bindings.sh) && wrangler pages dev ./build/client $bindings --ip 0.0.0.0 --port 5173 --no-show-interactive-dev-session", "dockerrun": "docker run -it -d --name bolt-ai-live -p 5173:5173 --env-file .env.local bolt-ai", "dockerbuild:prod": "docker build -t bolt-ai:production -t bolt-ai:latest --target bolt-ai-production .", "dockerbuild": "docker build -t bolt-ai:development -t bolt-ai:latest --target bolt-ai-development .", "typecheck": "tsc", "typegen": "wrangler types", "preview": "pnpm run build && pnpm run start", "prepare": "husky", "clean": "node scripts/clean.js", "electron:build:deps": "concurrently \"pnpm electron:build:main\" \"pnpm electron:build:preload\" --kill-others-on-fail", "electron:build:main": "vite build --config ./electron/main/vite.config.ts", "electron:build:preload": "vite build --config ./electron/preload/vite.config.ts", "electron:build:renderer": "remix vite:build --config vite-electron.config.js", "electron:build:unpack": "rm -rf dist && pnpm electron:build:renderer && pnpm electron:build:deps && electron-builder --dir", "electron:build:mac": "rm -rf dist && pnpm electron:build:renderer && pnpm electron:build:deps && electron-builder --mac", "electron:build:win": "rm -rf dist && pnpm electron:build:renderer && pnpm electron:build:deps && electron-builder --win", "electron:build:linux": "rm -rf dist && pnpm electron:build:renderer && pnpm electron:build:deps && electron-builder --linux", "electron:build:dist": "rm -rf dist && pnpm electron:build:renderer && pnpm electron:build:deps && electron-builder --mwl"}, "engines": {"node": ">=18.18.0"}, "dependencies": {"@ai-sdk/amazon-bedrock": "1.0.6", "@ai-sdk/anthropic": "0.0.39", "@ai-sdk/cohere": "1.0.3", "@ai-sdk/deepseek": "0.1.3", "@ai-sdk/google": "0.0.52", "@ai-sdk/mistral": "0.0.43", "@ai-sdk/openai": "1.1.2", "@codemirror/autocomplete": "^6.18.3", "@codemirror/commands": "^6.7.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.3.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-vue": "^0.1.3", "@codemirror/lang-wast": "^6.0.2", "@codemirror/language": "^6.10.6", "@codemirror/search": "^6.5.8", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.35.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@iconify-json/svg-spinners": "^1.2.1", "@lezer/highlight": "^1.2.1", "@nanostores/react": "^0.7.3", "@octokit/rest": "^21.0.2", "@octokit/types": "^13.6.2", "@openrouter/ai-sdk-provider": "^0.0.5", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@remix-run/cloudflare": "^2.15.2", "@remix-run/cloudflare-pages": "^2.15.2", "@remix-run/node": "^2.15.2", "@remix-run/react": "^2.15.2", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-virtual": "^3.13.0", "@types/react-beautiful-dnd": "^13.1.8", "@uiw/codemirror-theme-vscode": "^4.23.6", "@unocss/reset": "^0.61.9", "@webcontainer/api": "1.6.1-internal.1", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "4.1.2", "chalk": "^5.4.1", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "diff": "^5.2.0", "dotenv": "^16.4.7", "electron-log": "^5.2.3", "electron-store": "^10.0.0", "electron-updater": "^6.3.9", "file-saver": "^2.0.5", "framer-motion": "^11.12.0", "ignore": "^6.0.2", "isbot": "^4.4.0", "isomorphic-git": "^1.27.2", "istextorbinary": "^9.5.0", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jszip": "^3.10.1", "lucide-react": "^0.485.0", "mime": "^4.0.4", "nanostores": "^0.10.3", "ollama-ai-provider": "^0.15.2", "path-browserify": "^1.0.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hotkeys-hook": "^4.6.1", "react-icons": "^5.4.0", "react-markdown": "^9.0.1", "react-qrcode-logo": "^3.0.0", "react-resizable-panels": "^2.1.7", "react-toastify": "^10.0.6", "react-window": "^1.8.11", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remix-island": "^0.2.0", "remix-utils": "^7.7.0", "rollup-plugin-node-polyfills": "^0.2.1", "shiki": "^1.24.0", "tailwind-merge": "^2.2.1", "unist-util-visit": "^5.0.0", "use-debounce": "^10.0.4", "vite-plugin-node-polyfills": "^0.22.0", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@blitz/eslint-plugin": "0.1.0", "@cloudflare/workers-types": "^4.20241127.0", "@electron/notarize": "^2.5.0", "@iconify-json/ph": "^1.2.1", "@iconify/types": "^2.0.0", "@remix-run/dev": "^2.15.2", "@remix-run/serve": "^2.15.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/crypto-js": "^4.2.2", "@types/diff": "^5.2.3", "@types/dom-speech-recognition": "^0.0.4", "@types/electron": "^1.6.12", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/path-browserify": "^1.0.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "electron": "^33.2.0", "electron-builder": "^25.1.8", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "fast-glob": "^3.3.2", "husky": "9.1.7", "is-ci": "^3.0.1", "jsdom": "^26.0.0", "node-fetch": "^3.3.2", "pnpm": "^9.14.4", "prettier": "^3.5.3", "rimraf": "^4.4.1", "sass-embedded": "^1.81.0", "stream-browserify": "^3.0.0", "typescript": "^5.7.2", "unified": "^11.0.5", "unocss": "^0.61.9", "vite": "^5.4.11", "vite-plugin-copy": "^0.1.6", "vite-plugin-optimize-css-modules": "^1.1.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.7", "wrangler": "^4.5.1"}, "resolutions": {"@typescript-eslint/utils": "^8.0.0-alpha.30"}, "packageManager": "pnpm@9.4.0"}